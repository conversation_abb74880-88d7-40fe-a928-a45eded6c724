'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { authApi } from '@/lib/api';
import type { UserSettings } from '@/types/api';

export default function SettingsPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [settings, setSettings] = useState<UserSettings>({});
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const loadSettings = async () => {
      try {
        const response = await authApi.getSettings();
        if (mounted && response.success) {
          // Only update with defined values to prevent unnecessary re-renders
          const newSettings: UserSettings = {};
          Object.entries(response.settings).forEach(([key, value]) => {
            if (value !== undefined) {
              newSettings[key as keyof UserSettings] = value;
            }
          });
          setSettings(newSettings);
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to load settings');
        }
      } finally {
        if (mounted) {
          setIsInitialized(true);
        }
      }
    };

    loadSettings();
    return () => { mounted = false; };
  }, []);


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);
    setLoading(true);

    try {
      const response = await authApi.updateSettings(settings);
      if (response.success) {
        setSuccess(true);
      } else {
        setError(response.error || 'Failed to update settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (key: keyof UserSettings) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({ ...prev, [key]: e.target.value }));
  };

  if (!isInitialized) {
    return (
      <div className="max-w-2xl mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Settings</h1>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-4 space-y-8">
      <h1 className="text-2xl font-bold">Settings</h1>
      
      <form
        onSubmit={async (e) => {
          e.preventDefault();
          if (loading) return;
          await handleSubmit(e);
        }}
        className="space-y-6"
      >
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Drug Comparison Settings</h2>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <Input
              type="email"
              value={settings.drugComparisonEmail || ''}
              onChange={handleChange('drugComparisonEmail')}
              className="mt-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <Input
              type="password"
              value={settings.drugComparisonPassword || ''}
              onChange={handleChange('drugComparisonPassword')}
              className="mt-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">PIN (Optional)</label>
            <Input
              type="password"
              value={settings.drugComparisonPin || ''}
              onChange={handleChange('drugComparisonPin')}
              className="mt-1"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">IMAP Email Settings</h2>
          <div>
            <label className="block text-sm font-medium text-gray-700">IMAP Host</label>
            <Input
              type="text"
              value={settings.imapHost || ''}
              onChange={handleChange('imapHost')}
              className="mt-1"
              placeholder="e.g., imap.gmail.com"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">IMAP Port</label>
            <Input
              type="text"
              value={settings.imapPort || ''}
              onChange={handleChange('imapPort')}
              className="mt-1"
              placeholder="e.g., 993"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">IMAP Username</label>
            <Input
              type="text"
              value={settings.imapUser || ''}
              onChange={handleChange('imapUser')}
              className="mt-1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">IMAP Password</label>
            <Input
              type="password"
              value={settings.imapPassword || ''}
              onChange={handleChange('imapPassword')}
              className="mt-1"
            />
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Google Email Settings</h2>
          <div>
            <label className="block text-sm font-medium text-gray-700">Google Email</label>
            <Input
              type="email"
              value={settings.googleEmail || ''}
              onChange={handleChange('googleEmail')}
              className="mt-1"
              placeholder="e.g., <EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Google Account Status</label>
            {settings.googleEmail ? (
              <div className="mt-1 flex items-center justify-between p-2 border rounded-md bg-green-50 text-green-800">
                <span>Connected as: {settings.googleEmail}</span>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    // Handle disconnect logic here
                    setSettings(prev => ({ ...prev, googleEmail: undefined, googleRefreshToken: undefined }));
                  }}
                >
                  Disconnect
                </Button>
              </div>
            ) : (
              <Button
                type="button"
                onClick={() => {
                  // Initiate Google OAuth flow
                  window.location.href = '/api/auth/google-oauth'; // This will be a new backend route
                }}
                className="mt-1 w-full"
              >
                Connect Google Account
              </Button>
            )}
          </div>
        </div>

        {error && (
          <div className="text-red-500 text-sm">{error}</div>
        )}

        {success && (
          <div className="text-green-500 text-sm">Settings updated successfully</div>
        )}

        <Button
          type="submit"
          className="w-full"
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Settings'}
        </Button>
      </form>
    </div>
  );
}
