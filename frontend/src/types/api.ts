export interface InvoiceItem {
  Amount: number;
  unitPrice: number;
  Description: string;
  Quantity: number;
  itemId: number;
  pipCode: string;
  packSize: string;
}

export interface Invoice {
  id: string;
  VendorName: string;
  CustomerName: string;
  InvoiceDate: string;
  DueDate: string;
  InvoiceId: string;
  CustomerAddress: string;
  CustomerAddressRecipient: string;
  CustomerId: string;
  ShippingAddress: string;
  ShippingAddressRecipient: string;
  VendorAddress: string;
  VendorAddressRecipient: string;
  VendorTaxId: string;
  InvoiceTotal: number;
  Items: InvoiceItem[];
  orderReconciled: boolean;
  statementReconciled: boolean;
  processedAt: string;
  filePath: string;
}

export interface EmailProcessingResult {
  message?: string;
  processed: number;
  errors: Array<{
    type: string;
    message: string;
    code?: string;
  }>;
  details: Array<{
    status: string;
    reason?: string;
    attachments?: Array<{
      filename: string;
      status: string;
      message: string;
      documentType?: string;
      processed?: number;
    }>;
  }>;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

export interface UploadResponse {
  success: boolean;
  message: string;
  invoiceId: string;
  data: Invoice;
}

interface DateRange {
  startDate: string;
  endDate: string;
}

export interface OrdersResponse {
  success: boolean;
  message: string;
  ordersCount: number;
  totalScraped: number;
  skippedCount: number;
  dates?: DateRange;
  error?: {
    message: string;
    type?: string;
    details?: string;
  };
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: {
    uid: string;
    email: string;
  };
  error?: string;
}

export interface UserSettings {
  drugComparisonEmail?: string;
  drugComparisonPassword?: string;
  drugComparisonPin?: string;
  emailConnectionType?: 'imap' | 'google'; // New field for email connection type
  imapHost?: string;
  imapPort?: string;
  imapUser?: string;
  imapPassword?: string;
  googleEmail?: string; // New field for Google email
  googleRefreshToken?: string; // New field for Google OAuth Refresh Token
}

export interface SettingsResponse {
  success: boolean;
  settings: UserSettings;
  error?: string;
}
