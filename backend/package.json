{"name": "backend", "version": "1.0.0", "type": "module", "description": "", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "start": "node src/index.js", "postinstall": "npx puppeteer browsers install chrome"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase-admin": "^11.11.1", "googleapis": "^149.0.0", "imap": "^0.8.19", "jsonwebtoken": "^9.0.2", "mailparser": "^3.7.2", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "node-imap": "^0.9.6", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "puppeteer": "^24.3.0"}, "devDependencies": {"nodemon": "^3.0.1"}}