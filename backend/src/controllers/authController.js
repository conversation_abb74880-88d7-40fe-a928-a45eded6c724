import admin from 'firebase-admin';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { firebaseInitializationPromise } from '../config/firebase.js';
import { google } from 'googleapis'; // Import google from googleapis

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'; // Use environment variable in production

// Initialize Google OAuth2 client
const { OAuth2 } = google.auth;
const oAuth2Client = new OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3001/api/auth/google-oauth-callback' // Default for local development
);

export const login = async (req, res) => {
  try {
    console.log('Login attempt received');
    const { email, password } = req.body;
    
    console.log('Login credentials:', { email, hasPassword: !!password });
    
    if (!email || !password) {
      console.log('Missing email or password');
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    console.log('Attempting to initialize Firebase');
    let db;
    try {
      const result = await firebaseInitializationPromise;
      db = result.db;
      console.log('Firebase initialized successfully');
    } catch (firebaseError) {
      console.error('Firebase initialization error:', firebaseError);
      return res.status(500).json({
        success: false,
        error: 'Database connection failed'
      });
    }

    console.log('Querying users collection');
    // Get user from users collection
    const usersSnapshot = await db.collection('users')
      .where('email', '==', email)
      .limit(1)
      .get();

    let user;
    if (usersSnapshot.empty) {
      console.log('User not found, creating new user');
      // Create new user
      const hashedPassword = await bcrypt.hash(password, 10);
      const newUser = {
        email,
        password: hashedPassword,
        createdAt: new Date()
      };

      const userRef = await db.collection('users').add(newUser);
      console.log('New user created with ID:', userRef.id);
      user = {
        id: userRef.id,
        ...newUser
      };
    } else {
      console.log('User found, verifying password');
      user = {
        id: usersSnapshot.docs[0].id,
        ...usersSnapshot.docs[0].data()
      };

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);
      console.log('Password validation result:', isValidPassword);
      if (!isValidPassword) {
        console.log('Invalid password provided');
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }
    }

    console.log('Generating JWT token');
    // Generate JWT token
    try {
      const token = jwt.sign(
        {
          uid: user.id,
          email: user.email
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );
      
      console.log('JWT token generated successfully');
      console.log('JWT_SECRET used:', JWT_SECRET.substring(0, 3) + '...');
      
      res.json({
        success: true,
        token,
        user: {
          uid: user.id,
          email: user.email
        }
      });
    } catch (jwtError) {
      console.error('JWT signing error:', jwtError);
      return res.status(500).json({
        success: false,
        error: 'Failed to generate authentication token'
      });
    }

  } catch (error) {
    console.error('Login error:', error);
    console.error('Error stack:', error.stack);
    
    // Categorize the error
    let errorMessage = 'Authentication failed';
    let statusCode = 500;
    
    if (error.code === 'auth/wrong-password' || error.code === 'auth/user-not-found') {
      errorMessage = 'Invalid credentials';
      statusCode = 401;
    } else if (error.code === 'auth/invalid-email') {
      errorMessage = 'Invalid email format';
      statusCode = 400;
    } else if (error.code && error.code.startsWith('auth/')) {
      errorMessage = `Authentication error: ${error.code}`;
      statusCode = 401;
    } else if (error.name === 'FirebaseError') {
      errorMessage = 'Database operation failed';
    }
    
    console.error(`Responding with ${statusCode} status and message: ${errorMessage}`);
    
    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      ...(process.env.NODE_ENV === 'development' && { details: error.message })
    });
  }
};

/**
 * DEVELOPMENT ONLY: Returns the password for a test user.
 * Only use in development. Never deploy to production.
 */
export const devGetPassword = async (req, res) => {
  const { email } = req.query;
  // Hardcoded test users for development
  const testUsers = {
    "<EMAIL>": "testpassword123",
    "<EMAIL>": "adminpass",
    "<EMAIL>": "userpass",
    "<EMAIL>": "demopass",
    "<EMAIL>": "changeme"
  };
  if (email in testUsers) {
    return res.json({ success: true, password: testUsers[email] });
  }
  return res.status(404).json({ success: false, error: "User not found or not allowed." });
};

/**
 * Sends a Firebase password reset email using the REST API.
 * Expects { email } in req.body.
 */
export const sendPasswordReset = async (req, res) => {
  const { email } = req.body;
  if (!email) {
    return res.status(400).json({ success: false, error: "Email is required" });
  }
  try {
    const apiKey = process.env.FIREBASE_WEB_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ success: false, error: "FIREBASE_WEB_API_KEY not set in environment" });
    }
    const response = await fetch(
      `https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=${apiKey}`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ requestType: "PASSWORD_RESET", email })
      }
    );
    const data = await response.json();
    if (data.error) {
      return res.status(400).json({ success: false, error: data.error.message });
    }
    return res.json({ success: true });
  } catch (err) {
    return res.status(500).json({ success: false, error: "Failed to send password reset email" });
  }
};

export const getUserSettings = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const { uid } = req.user; // Set by auth middleware

    const settingsDoc = await db.collection('userSettings').doc(uid).get();
    
    if (!settingsDoc.exists) {
      return res.json({
        success: true,
        settings: {}
      });
    }

    res.json({
      success: true,
      settings: settingsDoc.data()
    });

  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch settings'
    });
  }
};

export const updateUserSettings = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const { uid } = req.user; // Set by auth middleware
    const settings = req.body;

    await db.collection('userSettings').doc(uid).set(settings, { merge: true });

    res.json({
      success: true,
      message: 'Settings updated successfully'
    });

  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to update settings'
    });
  }
};

// Google OAuth initiation
export const googleOAuth = (req, res) => {
  const scopes = [
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/gmail.readonly' // Scope to read emails
  ];

  // Store user ID in state to retrieve after callback
  const authUrl = oAuth2Client.generateAuthUrl({
    access_type: 'offline', // To get a refresh token
    scope: scopes,
    state: req.user.uid // Pass user ID to retrieve after callback
  });
  res.redirect(authUrl);
};

// Google OAuth callback
export const googleOAuthCallback = async (req, res) => {
  const { code, state: uid } = req.query; // Retrieve authorization code and user ID

  try {
    const { tokens } = await oAuth2Client.getToken(code);
    oAuth2Client.setCredentials(tokens);

    // Get user email from Google
    const oauth2 = google.oauth2({
      auth: oAuth2Client,
      version: 'v2'
    });
    const { data } = await oauth2.userinfo.get();
    const googleEmail = data.email;

    // Save refresh token and email to user settings
    const { db } = await firebaseInitializationPromise;
    await db.collection('userSettings').doc(uid).set(
      {
        googleEmail: googleEmail,
        googleRefreshToken: tokens.refresh_token,
        emailConnectionType: 'google' // Set Google as the preferred type
      },
      { merge: true }
    );

    // Redirect back to frontend settings page
    res.redirect('/admin/settings?googleAuthSuccess=true'); // Adjust frontend redirect path as needed

  } catch (error) {
    console.error('Error during Google OAuth callback:', error);
    res.redirect('/admin/settings?googleAuthError=true'); // Adjust frontend redirect path as needed
  }
};
