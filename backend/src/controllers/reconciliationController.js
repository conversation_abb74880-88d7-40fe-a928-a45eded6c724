import { firebaseInitializationPromise } from '../config/firebase.js';

// Helper function to format date for display (DD/MM/YYYY)
const formatFirestoreDateForReconciliation = (dateInput) => {
  if (!dateInput) return '';

  let d;
  if (typeof dateInput === 'string') {
    // Attempt to parse string in DD-MM-YYYY HH:mm, DD/MM/YYYY HH:mm, or DD/MM/YYYY
    const parts = dateInput.split(/[\s\/\-:]+/);
    if (parts.length >= 3) { // At least day, month, year
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
      const year = parseInt(parts[2].length === 2 ? `20${parts[2]}` : parts[2], 10);
      d = new Date(year, month, day);
    }
  } else if (dateInput.toDate) {
    // Handle Firestore Timestamp
    d = dateInput.toDate();
  } else if (dateInput instanceof Date) {
    d = dateInput;
  }

  if (d && !isNaN(d.getTime())) {
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    return `${day}/${month}/${year}`; // Format as DD/MM/YYYY
  }
  return ''; // Return empty string for invalid dates
};

// Helper function to normalize item identifiers (e.g., pipCode)
const normalizeItemId = (item) => {
  // Prioritize pipCode, fall back to description if necessary, handle potential variations
  const pip = item?.pipCode?.trim();
  if (pip && pip !== 'N/A' && pip !== '-') return pip;
  // Add more robust fallback/normalization if needed based on data patterns
  return item?.Description?.trim().toLowerCase() || `unknown-${Math.random()}`;
};

// Helper to compare items between order and invoice
const compareOrderInvoiceItems = (order, invoice) => {
  const discrepancies = [];
  const orderItemsMap = new Map();
  const invoiceItemsMap = new Map();

  // Index order items
  (order.Items || []).forEach(item => {
    const key = normalizeItemId(item);
    orderItemsMap.set(key, { ...(orderItemsMap.get(key) || { quantity: 0, price: item.price }), quantity: (orderItemsMap.get(key)?.quantity || 0) + (item.orderQty ?? item.Quantity ?? 0) });
  });

  // Index invoice items
  (invoice.Items || []).forEach(item => {
    const key = normalizeItemId(item);
    invoiceItemsMap.set(key, { ...(invoiceItemsMap.get(key) || { quantity: 0, price: item.unitPrice }), quantity: (invoiceItemsMap.get(key)?.quantity || 0) + (item.Quantity ?? 0) });
  });

  // Check items on order against invoice
  for (const [key, orderItemData] of orderItemsMap.entries()) {
    const invoiceItemData = invoiceItemsMap.get(key);
    const orderItem = (order.Items || []).find(i => normalizeItemId(i) === key); // Find representative item for description

    if (!invoiceItemData) {
      discrepancies.push({
        type: 'MISSING_FROM_INVOICE',
        itemId: key,
        description: orderItem?.description || orderItem?.Description || 'N/A',
        orderQty: orderItemData.quantity,
      });
    } else {
      if (orderItemData.quantity !== invoiceItemData.quantity) {
        discrepancies.push({
          type: 'QUANTITY_MISMATCH',
          itemId: key,
          description: orderItem?.description || orderItem?.Description || 'N/A',
          orderQty: orderItemData.quantity,
          invoiceQty: invoiceItemData.quantity,
        });
      }
      // Optional: Price comparison (handle potential nulls/zeros)
      const orderPrice = orderItemData.price;
      const invoicePrice = invoiceItemData.price;
       if (orderPrice != null && invoicePrice != null && Number(orderPrice) !== Number(invoicePrice)) {
         discrepancies.push({
           type: 'PRICE_MISMATCH',
           itemId: key,
           description: orderItem?.description || orderItem?.Description || 'N/A',
           orderPrice: orderPrice,
           invoicePrice: invoicePrice,
         });
       }
      // Mark invoice item as checked
      invoiceItemsMap.delete(key);
    }
  }

  // Check remaining items on invoice (not on order)
  for (const [key, invoiceItemData] of invoiceItemsMap.entries()) {
     const invoiceItem = (invoice.Items || []).find(i => normalizeItemId(i) === key); // Find representative item for description
    discrepancies.push({
      type: 'EXTRA_ON_INVOICE',
      itemId: key,
      description: invoiceItem?.Description || 'N/A',
      invoiceQty: invoiceItemData.quantity,
    });
  }

  return discrepancies;
};


// GET /api/reconciliation/summary - Enhanced for detailed discrepancies
export const getReconciliationSummary = async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    const userId = req.user.uid;

    // Fetch all orders, invoices, and statements for the user
    console.log(`Fetching reconciliation data for user: ${userId}`);
    const [ordersSnap, invoicesSnap, statementsSnap] = await Promise.all([
      db.collection('orders').where('userId', '==', userId).get(),
      db.collection('invoices').where('userId', '==', userId).get(),
      db.collection('statements').where('userId', '==', userId).get(),
    ]);
    console.log(`Fetched ${ordersSnap.size} orders, ${invoicesSnap.size} invoices, ${statementsSnap.size} statements.`);

    // Convert snapshots to arrays with IDs
    const orders = ordersSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const invoices = invoicesSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    const statements = statementsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // --- Data Preparation ---
    // Index invoices by orderId for quick lookup
    const invoicesByOrderId = new Map();
    invoices.forEach(inv => {
      if (inv.orderId) { // Ensure orderId exists
        if (!invoicesByOrderId.has(inv.orderId)) {
          invoicesByOrderId.set(inv.orderId, []);
        }
        invoicesByOrderId.get(inv.orderId).push(inv);
      }
    });

    // Index statement line items by Invoice Number for efficient lookup and amount comparison
    // ASSUMPTION: Statement documents have a 'lineItems' array like [{ invoiceNumber: '...', amount: ... }, ...]
    // Adjust 'lineItems', 'invoiceNumber', and 'amount' if the actual structure differs.
    const statementLineItemsMap = new Map();
    statements.forEach(stmt => {
      // Use stmt.invoices if that's where the detailed line items are stored
      const itemsSource = stmt.lineItems || stmt.invoices || [];
      if (Array.isArray(itemsSource)) {
        itemsSource.forEach(item => {
          // Ensure we have both an identifier (invoiceNumber) and an amount
          const invoiceNumber = item?.invoiceNumber || item?.InvoiceId || item?.invoiceId; // Adapt based on actual field name
          const amount = item?.amount || item?.Amount; // Adapt based on actual field name

          if (invoiceNumber && amount !== undefined && amount !== null) {
             // Handle potential duplicates? For now, last one wins. Consider aggregation if needed.
            statementLineItemsMap.set(String(invoiceNumber).trim(), parseFloat(amount));
          } else {
            // Log if essential data is missing from a statement line item
            // console.warn(`Statement ${stmt.id} has line item missing invoiceNumber or amount:`, item);
          }
        });
      }
    });
    console.log(`Indexed ${statementLineItemsMap.size} unique invoice numbers from statement line items.`);


    // --- Discrepancy Calculation ---
    const orderInvoiceDiscrepancies = [];
    const processedInvoiceIds = new Set(); // Keep track of invoices matched to orders

    // 1. Order vs. Invoice Comparison
    console.log("Starting Order vs. Invoice comparison...");
    for (const order of orders) {
      const matchingInvoices = invoicesByOrderId.get(order.id);

      if (!matchingInvoices || matchingInvoices.length === 0) {
        // Discrepancy: Order exists, but no invoice has its ID as orderId
        orderInvoiceDiscrepancies.push({
          type: 'MISSING_INVOICE',
          order: { // Include relevant order details
            id: order.id,
            orderNo: order.orderNo,
            dateTime: formatFirestoreDateForReconciliation(order.dateTime), // Apply formatting
            supplier: order.supplier,
            subTotal: order.subTotal,
          },
          details: 'No corresponding invoice found linking to this order ID.',
        });
      } else {
        // Order has one or more matching invoices, compare items
        // For simplicity, compare with the first matching invoice. Handle multiple matches if needed.
        const primaryInvoice = matchingInvoices[0];
        processedInvoiceIds.add(primaryInvoice.id); // Mark this invoice as processed

        const itemDiscrepancies = compareOrderInvoiceItems(order, primaryInvoice);

        if (itemDiscrepancies.length > 0) {
          orderInvoiceDiscrepancies.push({
            type: 'ITEM_MISMATCH',
             order: { // Include relevant order details
                id: order.id,
                orderNo: order.orderNo,
                dateTime: order.dateTime,
                supplier: order.supplier,
                subTotal: order.subTotal,
             },
             invoice: { // Include relevant invoice details
                id: primaryInvoice.id,
                InvoiceId: primaryInvoice.InvoiceId, // Invoice Number
                InvoiceDate: primaryInvoice.InvoiceDate,
                VendorName: primaryInvoice.VendorName,
                InvoiceTotal: primaryInvoice.InvoiceTotal,
             },
            details: itemDiscrepancies, // Array of specific item issues
          });
        }
         // Optional: Handle cases where an order might link to MULTIPLE invoices if that's possible
      }
    }
    console.log(`Found ${orderInvoiceDiscrepancies.length} Order vs. Invoice discrepancies.`);


    // 2. Invoice vs. Statement Comparison
    console.log("Starting Invoice vs. Statement comparison...");
    const invoiceStatementDiscrepancies = [];
    const tolerance = 0.01; // Tolerance for floating point comparisons

    for (const invoice of invoices) {
      const invoiceNumber = invoice.InvoiceId ? String(invoice.InvoiceId).trim() : null;
      const invoiceTotal = invoice.InvoiceTotal !== undefined && invoice.InvoiceTotal !== null ? parseFloat(invoice.InvoiceTotal) : null;

      if (!invoiceNumber) {
        // Cannot reconcile if the invoice itself doesn't have an InvoiceId
        // console.warn(`Invoice ${invoice.id} is missing an InvoiceId.`);
        continue; // Skip this invoice for statement comparison
      }

      if (invoiceTotal === null) {
         // Cannot reconcile if the invoice itself doesn't have a total
        // console.warn(`Invoice ${invoice.id} (${invoiceNumber}) is missing an InvoiceTotal.`);
         continue; // Skip this invoice for statement comparison
      }


      const statementAmount = statementLineItemsMap.get(invoiceNumber);

      if (statementAmount === undefined) {
        // Invoice number not found on any statement line item
        invoiceStatementDiscrepancies.push({
          type: 'MISSING_FROM_STATEMENT',
          invoice: {
            id: invoice.id,
            InvoiceId: invoiceNumber,
            InvoiceDate: invoice.InvoiceDate,
            VendorName: invoice.VendorName,
            InvoiceTotal: invoiceTotal,
          },
          details: `Invoice ${invoiceNumber} not found on any processed statement line item.`,
        });
      } else {
        // Invoice number found, compare amounts
        if (Math.abs(invoiceTotal - statementAmount) > tolerance) {
          // Amounts do not match within tolerance
          invoiceStatementDiscrepancies.push({
          type: 'AMOUNT_MISMATCH_STATEMENT',
          invoice: {
            id: invoice.id,
            InvoiceId: invoiceNumber,
            InvoiceDate: formatFirestoreDateForReconciliation(invoice.InvoiceDate), // Apply formatting
            VendorName: invoice.VendorName,
            InvoiceTotal: invoiceTotal,
          },
          statementAmount: statementAmount,
          details: `Amount mismatch for Invoice ${invoiceNumber}. Invoice: ${invoiceTotal.toFixed(2)}, Statement: ${statementAmount.toFixed(2)}`,
          });
        }
        // If amounts match, no discrepancy is added for this check.
      }
    }
    console.log(`Found ${invoiceStatementDiscrepancies.length} Invoice vs. Statement discrepancies.`);

    // --- Response ---
    res.json({
      success: true,
      // Return the detailed discrepancy lists instead of the old summary structure
      discrepancies: {
          orderInvoice: orderInvoiceDiscrepancies,
          invoiceStatement: invoiceStatementDiscrepancies,
      }
    });

  } catch (error) {
    console.error('Error in detailed reconciliation:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get detailed reconciliation data',
        details: error.message, // Provide more detail in dev if needed
      }
    });
  }
};
