import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.js';
import { scrapeOrders } from '../controllers/drugComparisonController.js';
import { firebaseInitializationPromise } from '../config/firebase.js';

const router = Router();

// Helper function to format date for display (DD-MM-YYYY HH:mm)
const formatDateForDisplay = (dateInput) => {
  if (!dateInput) return '';

  let d;
  if (typeof dateInput === 'string') {
    // Attempt to parse string in DD-MM-YYYY HH:mm or DD/MM/YYYY HH:mm
    const parts = dateInput.split(/[\s\/\-:]+/);
    if (parts.length >= 5) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
      const year = parseInt(parts[2].length === 2 ? `20${parts[2]}` : parts[2], 10);
      const hour = parseInt(parts[3], 10);
      const minute = parseInt(parts[4], 10);
      d = new Date(year, month, day, hour, minute);
    } else {
      // Fallback for simple date strings like DD-MM-YYYY or DD/MM/YYYY
      const dateOnlyParts = dateInput.split(/[\/\-]+/);
      if (dateOnlyParts.length === 3) {
        const day = parseInt(dateOnlyParts[0], 10);
        const month = parseInt(dateOnlyParts[1], 10) - 1;
        const year = parseInt(dateOnlyParts[2].length === 2 ? `20${dateOnlyParts[2]}` : dateOnlyParts[2], 10);
        d = new Date(year, month, day);
      }
    }
  } else if (dateInput.toDate) {
    // Handle Firestore Timestamp
    d = dateInput.toDate();
  } else if (dateInput instanceof Date) {
    d = dateInput;
  }

  if (d && !isNaN(d.getTime())) {
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${day}-${month}-${year} ${hours}:${minutes}`;
  }
  return ''; // Return empty string for invalid dates
};

// Protect all routes with authentication
router.use(checkAuth);

// GET /api/scrape/orders - Get all scraped orders
router.get('/orders', async (req, res) => {
  try {
    const { db } = await firebaseInitializationPromise;
    
    const userId = req.user.uid;
    const ordersRef = db.collection('orders');
    const q = ordersRef.where('userId', '==', userId);
    const ordersSnapshot = await q.get();
    
    const orders = [];
    ordersSnapshot.forEach(doc => {
      const data = doc.data();
      
      orders.push({
        id: doc.id,
        ...data,
        createdAt: formatDateForDisplay(data.createdAt),
        dateTime: formatDateForDisplay(data.dateTime),
        // Ensure numeric fields are numbers
        orderQty: parseFloat(data.orderQty) || 0,
        approvedQty: parseFloat(data.approvedQty) || 0,
        price: parseFloat(data.price) || 0,
        dtPrice: parseFloat(data.dtPrice) || 0,
        subTotal: parseFloat(data.subTotal) || 0,
        discount: parseFloat(data.discount) || 0
      });
    });

    res.setHeader('Content-Type', 'application/json');
    // Sort orders by createdAt descending
    orders.sort((a, b) => {
      // Parse formatted date strings for sorting
      const dateA = new Date(a.createdAt.replace(/-/g, '/')); // Convert to YYYY/MM/DD for reliable parsing
      const dateB = new Date(b.createdAt.replace(/-/g, '/'));
      return dateB.getTime() - dateA.getTime();
    });

    // Always return an array, even if empty
    res.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to fetch orders',
        details: error.message
      }
    });
  }
});

// POST /api/scrape/retrieve - Scrape orders from drugcomparison
router.post('/retrieve', async (req, res, next) => {
  console.log('Received request at /api/scrape/retrieve');
  console.log('Request body:', req.body);
  try {
    await scrapeOrders(req, res);
  } catch (error) {
    console.error('Error in scrape route:', error);
    next(error);
  }
});

export default router;
